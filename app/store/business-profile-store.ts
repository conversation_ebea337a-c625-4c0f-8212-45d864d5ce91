import type { BusinessProfile } from '~/types/business';
import { defineStore } from 'pinia';
import { getBusinessProfile } from '~/services/database/business-service';

/* ---------------------------------------------------------------------------------------------- */

export const useBusinessProfileStore = defineStore('businessProfile', () => {
   /*
    * States
    */

   const profile = ref<BusinessProfile | null>(null);
   const isLoading = ref(false);
   const error = ref<Error | null>(null);

   const businessProfileId = computed<string>(() => {
      if (profile.value && profile.value.id)
         return profile.value.id;
      return '';
   });

   /* ------------------------------------------------------------------------------------------- */

   // Fetch profile from API
   const fetchProfile = async (userId: string) => {
      if (!userId)
         return;

      isLoading.value = true;
      error.value = null;

      try {
         const client = useSupabaseClient();
         const data = await getBusinessProfile(client, userId);

         if (data) {
            // Store only the plain data without any methods or complex objects
            profile.value = JSON.parse(JSON.stringify(data));
         }
      }
      catch (err) {
         error.value = err as Error;
         console.error('Error fetching business profile:', err);
      }
      finally {
         isLoading.value = false;
      }
   };

   /* ------------------------------------------------------------------------------------------- */

   // Clear profile (for logout)
   const clearProfile = () => {
      profile.value = null;
   };

   /* ------------------------------------------------------------------------------------------- */

   return {
      profile,
      isLoading,
      error,
      businessProfileId,
      fetchProfile,
      clearProfile,
   };
});
