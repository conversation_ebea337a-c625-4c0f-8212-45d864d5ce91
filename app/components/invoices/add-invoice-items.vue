<script setup lang="ts">
import type { InvoiceCurrency, InvoiceFormItem } from '~/types/invoice';
import { IconsLibrary } from 'assets/icons';
import { toFormattedCurrency, toTitleCase } from '~/utils/string-utils';

/* ---------------------------------------------------------------------------------------------- */

const { currency } = defineProps<{
   currency: InvoiceCurrency;
}>();

// Invoice items state
const invoiceItems = defineModel<InvoiceFormItem[]>({ default: [] });

// Add a new invoice item
function addInvoiceItem() {
   invoiceItems.value.push({
      description: '',
      quantity: 1,
      unit_price: 0,
      total_price: 0,
      tax: 0,
      discount: 0,
      isNew: true,
   });
}

// Remove an invoice item
function removeInvoiceItem(index: number) {
   invoiceItems.value.splice(index, 1);
}

// Update the item total price when quantity or unit price changes
function updateItemTotal(item: InvoiceFormItem) {
   // Calculate total before tax and discount
   const subTotal = item.quantity * item.unit_price;
   // Calculate tax amount
   const taxAmount = subTotal * (item.tax / 100);
   // Calculate discount amount
   const discountAmount = subTotal * (item.discount / 100);
   // Calculate final total price
   item.total_price = subTotal + taxAmount - discountAmount;
}

// Computed total amount
const totalAmount = computed(() => {
   return invoiceItems.value.reduce((sum, item) => sum + item.total_price, 0);
});
</script>

<template>
   <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium">
         Invoice Items
      </h3>
      <UButton
         color="primary"
         variant="soft"
         size="sm"
         @click="addInvoiceItem"
      >
         <template #leading>
            <UIcon :name="IconsLibrary.actions.add" />
         </template>
         Add Item
      </UButton>
   </div>

   <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
         <thead class="bg-gray-50">
            <tr>
               <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
               </th>
               <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantity
               </th>
               <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Unit Price
               </th>
               <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tax (%)
               </th>
               <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Discount (%)
               </th>
               <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
               </th>
               <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
               </th>
            </tr>
         </thead>
         <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(item, index) in invoiceItems" :key="index" class="hover:bg-gray-50">
               <td class="px-6 py-4 whitespace-nowrap">
                  <UInput
                     v-model="item.description"
                     placeholder="Item description"
                     class="w-full"
                  />
               </td>
               <td class="px-6 py-4 whitespace-nowrap">
                  <UInputNumber
                     v-model="item.quantity"
                     class="w-32"
                     @update:model-value="updateItemTotal(item)"
                  />
               </td>
               <td class="px-6 py-4 whitespace-nowrap">
                  <UInputNumber
                     v-model="item.unit_price"
                     class="w-32"
                     @update:model-value="updateItemTotal(item)"
                  />
               </td>
               <td class="px-6 py-4 whitespace-nowrap">
                  <UInputNumber
                     v-model="item.tax"
                     class="w-32"
                     @update:model-value="updateItemTotal(item)"
                  />
               </td>
               <td class="px-6 py-4 whitespace-nowrap">
                  <UInputNumber
                     v-model="item.discount"
                     class="w-32"
                     @update:model-value="updateItemTotal(item)"
                  />
               </td>
               <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">
                     {{ toFormattedCurrency(item.total_price, currency) }}
                  </div>
               </td>
               <td class="px-6 py-4 whitespace-nowrap text-right">
                  <UButton
                     v-if="invoiceItems.length > 1"
                     color="error"
                     variant="ghost"
                     size="sm"
                     icon="i-heroicons-trash"
                     @click="removeInvoiceItem(index)"
                  />
               </td>
            </tr>
         </tbody>
         <tfoot>
            <tr>
               <td colspan="5" class="px-6 py-4 text-right font-medium">
                  Total Amount:
               </td>
               <td class="px-6 py-4 whitespace-nowrap font-bold text-lg">
                  {{ toFormattedCurrency(totalAmount, currency) }}
               </td>
               <td />
            </tr>
         </tfoot>
      </table>
   </div>
</template>

<style scoped>

</style>
