<script setup lang="ts">
import type { InvoiceStatus } from '~/types/invoice';

const props = defineProps<{
   status: InvoiceStatus;
   size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
   variant?: 'solid' | 'outline' | 'soft' | 'subtle';
}>();

// Default values for optional props
const size = props.size || 'md';
const variant = props.variant || 'subtle';

type InvoiceStatusColor = 'error' | 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'neutral';

// Map invoice status to appropriate colors
const statusColorMap: Record<InvoiceStatus, InvoiceStatusColor> = {
   draft: 'neutral',
   sent: 'primary',
   paid: 'success',
   overdue: 'error',
   cancelled: 'warning',
};

// Get color based on status
const color = computed(() => statusColorMap[props.status]);
</script>

<template>
   <UBadge
      :color="color"
      :variant="variant"
      :size="size"
      :label="status"
      class="uppercase font-bold"
   />
   
</template>

<style scoped>

</style>
