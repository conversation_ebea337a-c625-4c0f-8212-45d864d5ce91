<script setup lang="ts">
/* ---------------------------------------------------------------------------------------------- */

const { enableAll } = defineProps<{
   enableAll?: boolean;
}>();

const items = ref([
   { id: 'draft', label: 'Draft' },
   { id: 'sent', label: 'Sent' },
   { id: 'paid', label: 'Paid' },
   { id: 'overdue', label: 'Overdue' },
   { id: 'cancelled', label: 'Cancelled' },
]);

if (enableAll) {
   items.value.unshift({ id: 'all', label: 'All Statuses' });
}

const value = defineModel({ default: 'draft' });
</script>

<template>
   <USelectMenu
      v-model="value"
      :items="items"
      value-key="id"
      class="w-full"
   />
</template>

<style scoped>

</style>
