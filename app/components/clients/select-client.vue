<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types';
import type { BusinessClient } from '~/types/business-client';
import { IconsLibrary } from 'assets/icons';
import { storeToRefs } from 'pinia';
import * as v from 'valibot';

import {
   createBusinessClient,
   getBusinessClientById,
   getBusinessClients,
} from '~/services/database/business-client-service';
import { useBusinessProfileStore } from '~/store/business-profile-store';

/* ---------------------------------------------------------------------------------------------- */

const { size = 'md' } = defineProps<{
   label?: string;
   required?: boolean;
   name: string;
   selectOnly?: boolean; // disable create a new client if true
   size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}>();

/* ---------------------------------------------------------------------------------------------- */

interface SelectMenuItem {
   label: string;
   value: string;
   email: string;
   company: string;
}

/* ---------------------------------------------------------------------------------------------- */

// Get a business profile store and extract businessProfileId
const businessProfileStore = useBusinessProfileStore();
const { businessProfileId } = storeToRefs(businessProfileStore);

/* ---------------------------------------------------------------------------------------------- */

// Define model values
const selectedClient = defineModel<BusinessClient | undefined>('client');
const selectedClientId = defineModel<string | undefined>('client-id');

/* ---------------------------------------------------------------------------------------------- */

// State for clients' data
const clients = ref<BusinessClient[]>([]);
const clientSelectMenuItems = ref<SelectMenuItem[]>([]);
const selectedMenuItem = ref<SelectMenuItem | undefined>(undefined);
const isLoading = ref(true);
const error = ref<string | null>(null);
const searchTerm = ref('');

/* ---------------------------------------------------------------------------------------------- */

// Fetch clients from Supabase with search filtering
async function fetchClients() {
   isLoading.value = true;
   error.value = null;

   try {
      const supabase = useSupabaseClient();

      if (!businessProfileId.value) {
         error.value = 'Business profile not found';
         return;
      }

      const options = {
         searchQuery: searchTerm.value || undefined,
         sortBy: 'name',
         sortOrder: 'asc' as 'asc' | 'desc',
         activeOnly: true, // Only show active clients
      };

      clients.value = await getBusinessClients(supabase, businessProfileId.value, options);
   }
   catch (err: any) {
      console.error('Error fetching clients:', err);
      error.value = err.message || 'Failed to load clients';
   }
   finally {
      isLoading.value = false;
   }
}

// Fetch a single client by ID and set it as selected
async function fetchClientById(clientId: string) {
   try {
      const supabase = useSupabaseClient();
      const client = await getBusinessClientById(supabase, clientId);

      // Set the selected client
      selectedClient.value = client;

      // Set the selected menu item
      selectedMenuItem.value = {
         label: client.name,
         value: client.id,
         email: client.email,
         company: client.company_name || '',
      };

      // Add the client to the client's array if it's not already there
      const existingClientIndex = clients.value.findIndex(c => c.id === client.id);
      if (existingClientIndex === -1) {
         clients.value.unshift(client); // Add to the beginning of the array
      }
      else {
         console.error('Client already exists in clients array');
      }
   }
   catch (err: any) {
      console.error('Error fetching client by ID:', err);
      error.value = err.message || 'Failed to load selected client';
   }
}

// Watch for search term changes and refresh results with debouncing
watchDebounced(searchTerm, async (newValue) => {
   if (newValue !== undefined) {
      await fetchClients();
   }
}, { debounce: 300 });

watchDeep(clients, (clients) => {
   clientSelectMenuItems.value = clients.map(client => ({
      label: client.name,
      value: client.id,
      email: client.email,
      company: client.company_name || '',
   }));
});

/* ---------------------------------------------------------------------------------------------- */

watch(selectedMenuItem, (menuItem) => {
   if (menuItem) {
      selectedClientId.value = menuItem.value;
      selectedClient.value = clients.value.find(c => c.id === menuItem.value);
   }
   else {
      selectedClientId.value = undefined;
      selectedClient.value = undefined;
   }
});

// Watch for currentClientId changes and fetch client data if provided
watch(selectedClientId, async (newClientId, oldClientId) => {
   if (newClientId && newClientId !== oldClientId) {
      // Check if we already have this client selected
      if (selectedClient.value?.id === newClientId) {
         return;
      }

      await fetchClientById(newClientId);
   }
   else if (!newClientId) {
      // Clear selection if no client ID
      selectedClient.value = undefined;
      selectedMenuItem.value = undefined;
   }
}, { immediate: true });

/* ---------------------------------------------------------------------------------------------- */

// Ensure the business profile is loaded and fetch clients
onMounted(async () => {
   // If businessProfileId is not available, try to load it
   if (!businessProfileId.value) {
      const user = useSupabaseUser();
      if (user.value?.id) {
         await businessProfileStore.fetchProfile(user.value.id);
      }
   }

   await fetchClients();

   // If selectedClientId is provided but no client is selected, fetch the client
   if (selectedClientId.value && !selectedClient.value) {
      await fetchClientById(selectedClientId.value);
   }
});

/* ---------------------------------------------------------------------------------------------- */
// Client Creation Form
/* ---------------------------------------------------------------------------------------------- */

const showCreateClientForm = ref(false);

const createClientSchema = v.object({
   name: v.pipe(v.string(), v.minLength(1, 'Client name is required')),
   email: v.pipe(v.string(), v.email('Invalid email address')),
   company_name: v.optional(v.string()),
   phone: v.optional(v.string()),
   address: v.optional(v.string()),
});

type CreateClientSchemaType = v.InferOutput<typeof createClientSchema>;

const clientFormState = reactive({
   name: '',
   email: '<EMAIL>',
   phone: '',
   address: '',
   company_name: '',
});

const isSubmittingClient = ref(false);
const formError = ref('');

/* ---------------------------------------------------------------------------------------------- */

/**
 * Handle form submission
 */
async function handleClientSubmit(event: FormSubmitEvent<CreateClientSchemaType>) {
   isSubmittingClient.value = true;
   formError.value = '';

   try {
      const supabase = useSupabaseClient();

      if (!businessProfileId.value) {
         formError.value = 'Business profile not found';
         return;
      }

      // Use the same createBusinessClient service function
      const newClient = await createBusinessClient(supabase, businessProfileId.value, event.data);

      await fetchClients(); // Refresh the client list

      // Optionally, select the newly created client
      const newMenuItem = clientSelectMenuItems.value.find(item => item.value === newClient.id);
      if (newMenuItem) {
         selectedMenuItem.value = newMenuItem;
      }

      selectedClientId.value = newClient.id;

      showCreateClientForm.value = false; // Hide the form

      // Reset form fields
      Object.assign(clientFormState, { name: '', email: '', phone: '', address: '' });
   }
   catch (error: any) {
      console.error('Error saving client:', error);
      formError.value = error.message || 'Failed to save client. Please try again.';
   }
   finally {
      isSubmittingClient.value = false;
   }
}

/* ---------------------------------------------------------------------------------------------- */

function cancelCreateClient() {
   showCreateClientForm.value = false;
   formError.value = '';
   Object.assign(clientFormState, { name: '', email: '', phone: '', address: '' });
}

/* ---------------------------------------------------------------------------------------------- */

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <UPopover class="w-full">
      <UButton
         block
         :label="selectedClient ? selectedClient.name : 'Select a client'"
         :icon="selectedClient ? undefined : IconsLibrary.pages.client"
         variant="outline"
         color="neutral"
         :size
      />

      <template #content>
         <div class="p-4 w-96">
            <div class="flex flex-col gap-4">
               <!-- region: select menu -->
               <section v-if="!showCreateClientForm">
                  <USelectMenu
                     v-model="selectedMenuItem"
                     v-model:search-term="searchTerm"
                     :items="clientSelectMenuItems"
                     :loading="isLoading"
                     placeholder="Search clients..."
                     class="w-full"
                     searchable
                  >
                     <template #item="{ item }">
                        <div class="flex flex-col gap-1">
                           <div class="font-bold">
                              {{ item.label }}
                           </div>
                           <div class="text-sm text-gray-500">
                              {{ item.company || '-' }}
                           </div>
                           <div class="text-sm text-gray-400">
                              {{ item.email }}
                           </div>
                        </div>
                     </template>

                     <template #empty>
                        <div class="p-2 text-center text-gray-500">
                           <div v-if="searchTerm">
                              No clients found matching "{{ searchTerm }}"
                           </div>
                           <div v-else>
                              No clients available
                           </div>
                        </div>
                     </template>
                  </USelectMenu>
               </section>
               <!-- endregion: select menu -->

               <!-- region: create new client -->
               <section v-if="!selectOnly">
                  <UButton
                     v-if="!showCreateClientForm"
                     label="Create new client"
                     :icon="IconsLibrary.actions.create"
                     color="primary"
                     variant="soft"
                     class="w-full mt-2"
                     @click="showCreateClientForm = !showCreateClientForm"
                  />

                  <!-- region: add client form -->
                  <section v-if="showCreateClientForm" class="mt-4">
                     <h2 class="text-xl font-bold mb-2">
                        Create a new client
                     </h2>

                     <div class="border border-gray-200 dark:border-gray-700 rounded-md p-4">
                        <UForm
                           :schema="createClientSchema"
                           :state="clientFormState"
                           class="flex flex-col gap-4 w-full"
                           :attach="false"
                           @submit="handleClientSubmit"
                        >
                           <UFormField label="Client Name" name="name" required class="w-full">
                              <UInput
                                 v-model="clientFormState.name"
                                 placeholder="Enter client name"
                                 class="w-full"
                              />
                           </UFormField>

                           <UFormField label="Email Address" name="email" required class="w-full">
                              <UInput
                                 v-model="clientFormState.email"
                                 type="email"
                                 placeholder="<EMAIL>"
                                 class="w-full"
                              />
                           </UFormField>

                           <UFormField label="Business name" name="company_name" class="w-full">
                              <UInput
                                 v-model="clientFormState.company_name"
                                 placeholder="Enter company / business name"
                                 class="w-full"
                              />
                           </UFormField>

                           <UFormField label="Phone Number" name="phone" class="w-full">
                              <UInput
                                 v-model="clientFormState.phone"
                                 placeholder="(*************"
                                 class="w-full"
                              />
                           </UFormField>

                           <UFormField label="Address" name="address" class="w-full">
                              <UTextarea
                                 v-model="clientFormState.address"
                                 placeholder="Enter client's address"
                                 :rows="3"
                                 class="w-full"
                              />
                           </UFormField>

                           <div v-if="formError" class="mt-2">
                              <UAlert
                                 color="error"
                                 variant="soft"
                                 title="Error"
                                 :description="formError"
                                 @close="formError = ''"
                              />
                           </div>

                           <footer class="flex justify-end gap-3 pt-2">
                              <UButton
                                 variant="outline"
                                 color="neutral"
                                 @click="cancelCreateClient"
                              >
                                 Cancel
                              </UButton>

                              <UButton
                                 type="submit"
                                 color="primary"
                                 :loading="isSubmittingClient"
                              >
                                 Save Client
                              </UButton>
                           </footer> <!-- form footer -->
                        </UForm> <!-- form -->
                     </div>
                  </section>
                  <!-- endregion: add client form -->
               </section>
               <!-- endregion: create new client -->
            </div>
         </div>
      </template>
   </UPopover>
</template>

<style scoped>
</style>
