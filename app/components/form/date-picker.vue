<script setup lang="ts">
import type { DateValue } from '@internationalized/date';
import { getLocalTimeZone, parseDate } from '@internationalized/date';
import { IconsLibrary } from 'assets/icons';
import { toISODateString } from '~/utils/datetime-utils';

/* ---------------------------------------------------------------------------------------------- */

const { size = 'md' } = defineProps<{
   size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}>();

/* ---------------------------------------------------------------------------------------------- */

const model = defineModel<string>({ default: '' });

const dateObject = shallowRef<DateValue | null>(model.value ? parseDate(model.value) : null);

watch(dateObject, (date) => {
   model.value = date ? toISODateString(date.toDate(getLocalTimeZone())) : '';
});

watch(model, (date) => {
   dateObject.value = date ? parseDate(date) : null;
});
</script>

<template>
   <UPopover>
      <UButton :size="size" color="neutral" variant="subtle" :icon="IconsLibrary.components.calendar">
         {{ model ? model : 'Select a date' }}
      </UButton>

      <template #content>
         <div class="p-2">
            <UCalendar v-model="dateObject" class="mb-2" />
            <UButton
               v-if="dateObject"
               size="sm"
               color="secondary"
               variant="soft"
               block
               @click="dateObject = null"
            >
               Clear date
            </UButton>
         </div>
      </template>
   </UPopover>
</template>

<style scoped>

</style>
