import { useBusinessProfileStore } from '~/store/business-profile-store';

export default defineNuxtRouteMiddleware(async (to) => {
   const excludedPages = ['/sign-in', '/sign-up', '/welcome', '/callback'];

   if (excludedPages.includes(to.path))
      return;

   const user = useSupabaseUser();
   const profileStore = useBusinessProfileStore();

   if (user.value) {
      // If no profile in state, try to fetch it
      if (!profileStore.profile) {
         try {
            await profileStore.fetchProfile(user.value.id);

            // If still no profile after fetch, redirect to setup
            if (!profileStore.profile && to.path !== '/user/setup') {
               return navigateTo('/user/setup');
            }
         }
         catch (error) {
            console.error('Error fetching business profile:', error);
         }
      }
   }
});
