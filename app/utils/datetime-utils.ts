import dayjs from 'dayjs';

/* ---------------------------------------------------------------------------------------------- */

/**
 * Format a Date object into YYYY-MM-DD format
 * @param date - The Date object to format
 * @returns Formatted date string in YYYY-MM-DD format
 */
export function toISODateString(date: Date): string {
   return dayjs(date).format('YYYY-MM-DD');
}

/**
 * Get today's date in YYYY-MM-DD format
 */
export function getTodayDateString() {
   return dayjs().format('YYYY-MM-DD');
}
