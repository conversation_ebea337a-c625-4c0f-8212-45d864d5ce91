import type { InvoiceCurrency } from '~/types/invoice';

/**
 * String utility functions.
 */
export function toFormattedCurrency(amount: number, currency: InvoiceCurrency): string {
   return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
   }).format(amount);
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Converts a string to a title case.
 */
export function toTitleCase(text: string): string {
   return text
      .toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
}
