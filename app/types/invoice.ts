/* ---------------------------------------------------------------------------------------------- */

export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
export type InvoiceCurrency = 'USD' | 'EUR' | 'LKR';

/* ---------------------------------------------------------------------------------------------- */

export interface Invoice {
   id: string;
   business_id: string;
   client_id: string;
   invoice_number?: number;
   issue_date: string;
   due_date: string;
   total: number;
   status: InvoiceStatus;
   notes?: string | null;
   currency?: string | null;
   pdf_url?: string | null;
   public_link_token?: string | null;
   created_at: string;
   updated_at: string;

   // Joined fields (not in the database table)
   client_name?: string;
   client_email?: string;
   items_count?: number;
}

/* ---------------------------------------------------------------------------------------------- */

export interface InvoiceCreate {
   business_id: string;
   client_id: string;
   invoice_number?: number;
   issue_date: string;
   due_date: string;
   total: number;
   status: InvoiceStatus;
   notes?: string | null;
   currency?: string | null;
   pdf_url?: string | null;
   public_link_token?: string | null;
}

/* ---------------------------------------------------------------------------------------------- */

export interface InvoiceUpdate {
   client_id?: string;
   invoice_number?: number;
   issue_date?: string;
   due_date?: string;
   total?: number;
   status?: InvoiceStatus;
   notes?: string | null;
   currency?: string | null;
   pdf_url?: string | null;
   public_link_token?: string | null;
}

export interface InvoiceItem {
   id: string;
   invoice_id: string;
   description: string;
   quantity: number;
   unit_price: number;
   total_price: number;
   created_at: string;
   updated_at: string;
}

export interface InvoiceItemCreate {
   invoice_id: string;
   description: string;
   quantity: number;
   unit_price: number;
   total_price: number;
}

export interface InvoiceItemUpdate {
   description?: string;
   quantity?: number;
   unit_price?: number;
   total_price?: number;
}

export interface InvoiceWithItems extends Invoice {
   items: InvoiceItem[];
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Invoice item for the form
 */
export interface InvoiceFormItem {
   id?: string;
   description: string;
   quantity: number;
   unit_price: number;
   total_price: number;
   tax: number;
   discount: number;
   isNew?: boolean;
}
