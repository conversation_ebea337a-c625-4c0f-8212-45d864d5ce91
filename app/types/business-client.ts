export interface BusinessClient {
   id: string;
   created_at: string;
   updated_at: string;
   business_id: string;
   client_number: number | null;
   name: string;
   email: string;
   address: string | null;
   phone: string | null;
   company_name: string | null;
   deleted_on: string | null;
   active: boolean;
}

export interface BusinessClientCreate {
   name: string;
   email: string;
   client_number?: number | null;
   address?: string | null;
   phone?: string | null;
   company_name?: string | null;
}

export interface BusinessClientUpdate {
   name?: string;
   email?: string;
   client_number?: number | null;
   address?: string | null;
   phone?: string | null;
   company_name?: string | null;
   active?: boolean;
}
