import type { SupabaseClient } from '@supabase/supabase-js';
import type { BusinessClient, BusinessClientCreate, BusinessClientUpdate } from '~/types/business-client';

/* ---------------------------------------------------------------------------------------------- */

/**
 * Create a new business client
 */
export async function createBusinessClient(
   client: SupabaseClient,
   businessId: string,
   clientData: BusinessClientCreate,
): Promise<BusinessClient> {
   // Prepare client data with business_id and timestamps
   const data = {
      ...clientData,
      business_id: businessId,
      updated_at: new Date().toISOString(),
   };

   const { data: newClient, error } = await client
      .from('clients')
      .insert(data)
      .select()
      .single();

   if (error)
      throw error;

   return newClient;
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Get all business clients for a business with optional filtering
 */
export async function getBusinessClients(
   client: SupabaseClient,
   businessId: string,
   options?: {
      searchQuery?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      limit?: number;
      includeDeleted?: boolean;
      activeOnly?: boolean;
   },
): Promise<BusinessClient[]> {
   let query = client
      .from('clients')
      .select('*')
      .eq('business_id', businessId);

   // Filter out deleted clients unless specifically requested
   if (!options?.includeDeleted) {
      query = query.is('deleted_on', null);
   }

   // Filter by active status if requested
   // When activeOnly is true, only show active clients
   // When activeOnly is false, show all clients (both active and inactive)
   if (options?.activeOnly) {
      query = query.eq('active', true);
   }

   // Apply search filter if provided
   if (options?.searchQuery) {
      const searchTerm = `%${options.searchQuery}%`;
      query = query.or(
         `name.ilike.${searchTerm},email.ilike.${searchTerm},company_name.ilike.${searchTerm}`,
      );
   }

   // Apply sorting
   const sortField = options?.sortBy || 'name';
   const sortOrder = options?.sortOrder || 'asc';
   query = query.order(sortField, { ascending: sortOrder === 'asc' });

   // Apply limit if provided
   if (options?.limit) {
      query = query.limit(options.limit);
   }

   const { data, error } = await query;

   if (error)
      throw error;

   return data || [];
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Get a single business client by ID
 */
export async function getBusinessClientById(
   client: SupabaseClient,
   clientId: string,
   includeDeleted: boolean = false,
): Promise<BusinessClient> {
   let query = client
      .from('clients')
      .select('*')
      .eq('id', clientId);

   // Filter out deleted clients unless specifically requested
   if (!includeDeleted) {
      query = query.is('deleted_on', null);
   }

   const { data, error } = await query.single();

   if (error) {
      if (error.code === 'PGRST116') {
         throw new Error(`Client with ID ${clientId} not found`);
      }
      throw error;
   }

   return data;
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Update an existing business client
 */
export async function updateBusinessClient(
   client: SupabaseClient,
   clientId: string,
   clientData: BusinessClientUpdate,
): Promise<BusinessClient> {
   const data = {
      ...clientData,
      updated_at: new Date().toISOString(),
   };

   const { data: updatedClient, error } = await client
      .from('clients')
      .update(data)
      .eq('id', clientId)
      .select()
      .single();

   if (error)
      throw error;

   return updatedClient;
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Softly delete a client by setting the deleted_on timestamp
 */
export async function softDeleteClient(
   client: SupabaseClient,
   clientId: string,
): Promise<BusinessClient> {
   // First, verify the client exists and is not yet deleted
   const { error: fetchError } = await client
      .from('clients')
      .select('id, deleted_on')
      .eq('id', clientId)
      .is('deleted_on', null)
      .single();

   if (fetchError) {
      if (fetchError.code === 'PGRST116') {
         throw new Error(`Client with ID ${clientId} not found or already deleted`);
      }
      throw fetchError;
   }

   // Set the deleted_on timestamp to the current time
   const { data: deletedClient, error } = await client
      .from('clients')
      .update({
         deleted_on: new Date().toISOString(),
         updated_at: new Date().toISOString(),
      })
      .eq('id', clientId)
      .select()
      .single();

   if (error) {
      throw error;
   }

   return deletedClient;
}

/* ---------------------------------------------------------------------------------------------- */

/**
 * Restore a soft-deleted client by clearing the deleted_on timestamp
 */
export async function restoreClient(
   client: SupabaseClient,
   clientId: string,
): Promise<BusinessClient> {
   // First, verify the client exists and is deleted
   const { error: fetchError } = await client
      .from('clients')
      .select('id, deleted_on')
      .eq('id', clientId)
      .not('deleted_on', 'is', null)
      .single();

   if (fetchError) {
      if (fetchError.code === 'PGRST116') {
         throw new Error(`Client with ID ${clientId} not found or not deleted`);
      }
      throw fetchError;
   }

   // Clear the deleted_on timestamp
   const { data: restoredClient, error } = await client
      .from('clients')
      .update({
         deleted_on: null,
         updated_at: new Date().toISOString(),
      })
      .eq('id', clientId)
      .select()
      .single();

   if (error) {
      throw error;
   }

   return restoredClient;
}
