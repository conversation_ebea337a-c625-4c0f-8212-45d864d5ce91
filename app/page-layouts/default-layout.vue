<script setup lang="ts">
import { breakpointsTailwind, useBreakpoints } from '@vueuse/core';
import { IconsLibrary } from 'assets/icons';
import AppSidebar from '~/page-layouts/sidebar/app-sidebar.vue';

/* ---------------------------------------------------------------------------------------------- */

const isSidebarOpen = ref(false);

function toggleSidebar() {
   isSidebarOpen.value = !isSidebarOpen.value;
}

// Breakpoints for responsive design
const breakpoints = useBreakpoints(breakpointsTailwind);

// Close sidebar on mobile when route changes
const route = useRoute();
watch(() => route.path, () => {
   if (breakpoints.smallerOrEqual('sm').value) {
      isSidebarOpen.value = false;
   }
});

// Initialize sidebar state based on screen size
onMounted(() => {
   isSidebarOpen.value = breakpoints.greater('lg').value;

   // Update sidebar state on window resize
   window.addEventListener('resize', () => {
      isSidebarOpen.value = breakpoints.greater('lg').value;
   });
});
</script>

<template>
   <div class="min-h-screen flex">
      <!-- Sidebar Component -->

      <AppSidebar
         :is-sidebar-open="isSidebarOpen"
         @toggle-sidebar="toggleSidebar"
      />

      <!-- Main content -->
      <main class="flex-1 flex overflow-auto flex-col transition-all duration-300">
         <!-- Toggle button for mobile -->
         <div class="p-4 md:hidden">
            <UButton
               :icon="IconsLibrary.components.menu"
               size="xl"
               color="primary"
               variant="soft"
               @click="toggleSidebar"
            />
         </div>

         <!-- Container for content -->
         <div class="mx-auto flex flex-col overflow-auto w-screen md:w-full max-w-7xl px-4 py-6">
            <slot />
         </div>
      </main>
   </div>
</template>

<style scoped>

</style>
