<script setup lang="ts">
import { useAppVersion } from '~~/composables/use-app-version';
import Logo from '~/components/branding/logo.vue';

/* ---------------------------------------------------------------------------------------------- */

defineProps<{
   isSidebarOpen: boolean;
}>();

const emit = defineEmits<{
   toggleSidebar: [];
}>();

function toggleSidebar() {
   emit('toggleSidebar');
}

const route = useRoute();
const { version } = useAppVersion();
</script>

<template>
   <div>
      <!-- Sidebar overlay for mobile -->
      <div
         v-if="isSidebarOpen"
         class="fixed inset-0 bg-black/20 z-10 md:hidden"
         @click="toggleSidebar"
      />

      <!-- Sidebar -->
      <aside
         class="fixed md:sticky  top-0 z-20 h-screen bg-white border-r border-gray-200 transition-all duration-300 flex flex-col"
         :class="isSidebarOpen ? 'w-64 left-0' : 'w-64 -left-64 md:left-0 md:w-16'"
      >
         <!-- Logo and close button -->
         <div class="p-4 flex flex-col">
            <div class="flex items-center justify-center">
               <Logo v-if="isSidebarOpen" class="transition-all scale-90" />
               <div v-else class="text-2xl font-extrabold">
                  <span class="text-black">A</span><span class="text-primary">I</span>
               </div>
            </div>
            <!-- Version number -->
            <div
               v-if="isSidebarOpen"
               class="text-xs font-bold text-gray-300 flex w-full justify-end"
            >
               v{{ version }}
            </div>
         </div>

         <!-- Main navigation -->
         <div class="p-2 flex-grow overflow-y-auto">
            <div class="flex flex-col gap-2">
               <UButton
                  block
                  :variant="isSidebarOpen ? 'soft' : 'ghost'"
                  :color="route.path === '/dashboard' ? 'primary' : 'neutral'"
                  size="xl"
                  class="justify-start"
                  to="/dashboard"
               >
                  <template #leading>
                     <UIcon name="i-heroicons-home-modern" />
                  </template>
                  <span v-if="isSidebarOpen">Dashboard</span>
               </UButton>

               <UButton
                  block
                  :variant="isSidebarOpen ? 'soft' : 'ghost'"
                  :color="route.path.includes('/invoices') ? 'primary' : 'neutral'"
                  size="xl"
                  class="justify-start"
                  to="/invoices"
               >
                  <template #leading>
                     <UIcon name="i-heroicons-document-chart-bar" />
                  </template>
                  <span v-if="isSidebarOpen">Invoices</span>
               </UButton>

               <UButton
                  block
                  :variant="isSidebarOpen ? 'soft' : 'ghost'"
                  :color="route.path.includes('/clients') ? 'primary' : 'neutral'"
                  size="xl"
                  class="justify-start"
                  to="/clients"
               >
                  <template #leading>
                     <UIcon name="i-heroicons-user-group" />
                  </template>
                  <span v-if="isSidebarOpen">Clients</span>
               </UButton>

               <UButton
                  block
                  :variant="isSidebarOpen ? 'soft' : 'ghost'"
                  :color="route.path.includes('/products') ? 'primary' : 'neutral'"
                  size="xl"
                  class="justify-start"
                  to="/products"
               >
                  <template #leading>
                     <UIcon name="i-heroicons-shopping-bag" />
                  </template>
                  <span v-if="isSidebarOpen">Products</span>
               </UButton>

               <UButton
                  block
                  :variant="isSidebarOpen ? 'soft' : 'ghost'"
                  :color="route.path.includes('/reports') ? 'primary' : 'neutral'"
                  size="xl"
                  class="justify-start"
                  to="/reports"
               >
                  <template #leading>
                     <UIcon name="i-heroicons-presentation-chart-line" />
                  </template>
                  <span v-if="isSidebarOpen">Reports</span>
               </UButton>

               <UButton
                  block
                  :variant="isSidebarOpen ? 'soft' : 'ghost'"
                  :color="route.path.includes('/settings') ? 'primary' : 'neutral'"
                  size="xl"
                  class="justify-start"
                  to="/settings"
               >
                  <template #leading>
                     <UIcon name="i-heroicons-adjustments-horizontal" />
                  </template>
                  <span v-if="isSidebarOpen">Settings</span>
               </UButton>
            </div>
         </div>

         <!-- Profile button at bottom -->
         <div class="p-2 border-t border-gray-200">
            <UButton
               block
               :variant="isSidebarOpen ? 'soft' : 'ghost'"
               :color="route.path.includes('/profile') ? 'primary' : 'neutral'"
               size="xl"
               class="justify-start"
               to="/profile"
            >
               <template #leading>
                  <UIcon name="i-heroicons-identification" />
               </template>
               <span v-if="isSidebarOpen">Profile</span>
            </UButton>
         </div>
      </aside>
   </div>
</template>

<style scoped>
aside {
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
}
</style>
