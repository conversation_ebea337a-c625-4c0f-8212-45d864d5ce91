@import "tailwindcss";
@import "@nuxt/ui";


@import "tailwindcss";
@import "@nuxt/ui";


@theme static{

   --font-sans: "Manrope", sans-serif;

   /* Nuxt ui colors */
   --ui-primary: var(--ui-color-primary-500);
   --ui-secondary: var(--ui-color-secondary-500);
   --ui-color-primary-50: oklch(97.7% 0.013 236.62);
   --ui-color-primary-100: oklch(95.1% 0.026 236.824);
   --ui-color-primary-200: oklch(90.1% 0.058 230.902);
   --ui-color-primary-300: oklch(82.8% 0.111 230.318);
   --ui-color-primary-400: oklch(74.6% 0.16 232.661);
   --ui-color-primary-500: oklch(68.5% 0.169 237.323);
   --ui-color-primary-600: oklch(58.8% 0.158 241.966);
   --ui-color-primary-700: oklch(50% 0.134 242.749);
   --ui-color-primary-800: oklch(44.3% 0.11 240.79);
   --ui-color-primary-900: oklch(39.1% 0.09 240.876);

   --ui-color-secondary-50: oklch(98.4% 0.003 247.858);
   --ui-color-secondary-100: oklch(96.8% 0.007 247.896);
   --ui-color-secondary-200: oklch(92.9% 0.013 255.508);
   --ui-color-secondary-300: oklch(86.9% 0.022 252.894);
   --ui-color-secondary-400: oklch(70.4% 0.04 256.788);
   --ui-color-secondary-500: oklch(55.4% 0.046 257.417);
   --ui-color-secondary-600: oklch(44.6% 0.043 257.281);
   --ui-color-secondary-700: oklch(37.2% 0.044 257.287);
   --ui-color-secondary-800: oklch(27.9% 0.041 260.031);
   --ui-color-secondary-900: oklch(20.8% 0.042 265.755);


}




@layer base{
   body{
      @apply m-0 min-w-[320px] min-h-dvh;
   }

   a.link{
      @apply text-primary-500 transition-all;
      @apply hover:text-primary-600 hover:underline;
   }
}


@layer components{
   .card {
      @apply p-4 rounded-2xl shadow-lg outline outline-primary-200 bg-white;
   }
}

#app {
   width: 100%;
}

