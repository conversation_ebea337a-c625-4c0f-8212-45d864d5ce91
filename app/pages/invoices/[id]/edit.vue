<script setup lang="ts">
// This is a redirect page that will navigate to the invoice detail page
// and automatically open the edit mode

const route = useRoute();
const invoiceId = route.params.id as string;

// Navigate to the invoice detail page
onMounted(() => {
   navigateTo(`/invoices/${invoiceId}`);
});
</script>

<template>
   <DefaultLayout>
      <div class="flex justify-center items-center h-64">
         <UIcon name="i-heroicons-arrow-path" class="animate-spin h-8 w-8 text-gray-500" />
      </div>
   </DefaultLayout>
</template>
