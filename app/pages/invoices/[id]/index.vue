<script setup lang="ts">
import type { BusinessClient } from '~/types/business-client';
import type { InvoiceFormItem, InvoiceStatus, InvoiceWithItems } from '~/types/invoice';
import { useSupabaseClient } from '#imports';
import { getInvoiceWithItems, updateInvoiceWithItems, deleteInvoice } from '~/services/database/invoice-service';
import { getBusinessClients } from '~/services/database/business-client-service';
import { useBusinessProfileStore } from '~/store/business-profile-store';

/* ---------------------------------------------------------------------------------------------- */

const route = useRoute();
const invoiceId = route.params.id as string;
const businessProfileStore = useBusinessProfileStore();
const { businessProfileId } = storeToRefs(businessProfileStore);
const toast = useToast();

/* ---------------------------------------------------------------------------------------------- */

// State
const invoice = ref<InvoiceWithItems | null>(null);
const clients = ref<BusinessClient[]>([]);
const isLoading = ref(true);
const isSubmitting = ref(false);
const isEditing = ref(false);
const error = ref<string | null>(null);

// Form state
const invoiceFormState = reactive({
   client_id: '',
   issue_date: '', // Changed from invoice_date
   due_date: '',
   status: '' as InvoiceStatus,
   currency: '', // New field
   notes: '',
});

// Invoice items state
const invoiceItems = ref<InvoiceFormItem[]>([]);

// Status options for dropdown
const statusOptions = [
   { value: 'draft', label: 'Draft' },
   { value: 'sent', label: 'Sent' },
   { value: 'paid', label: 'Paid' },
   { value: 'overdue', label: 'Overdue' },
   { value: 'cancelled', label: 'Cancelled' },
];

// Status color mapping
const statusColorMap: Record<InvoiceStatus, string> = {
   draft: 'gray',
   sent: 'primary',
   paid: 'success',
   overdue: 'danger',
   cancelled: 'warning',
};

// Computed total amount
const totalAmount = computed(() => {
   return invoiceItems.value.reduce((sum, item) => sum + item.total_price, 0);
});

/* ---------------------------------------------------------------------------------------------- */

// Fetch invoice data
async function fetchInvoice() {
   isLoading.value = true;
   error.value = null;

   try {
      const supabase = useSupabaseClient();

      invoice.value = await getInvoiceWithItems(supabase, invoiceId);

      // Initialize form state with invoice data
      invoiceFormState.client_id = invoice.value.client_id;
      invoiceFormState.issue_date = invoice.value.issue_date.split('T')[0]; // Changed from invoice_date
      invoiceFormState.due_date = invoice.value.due_date.split('T')[0];
      invoiceFormState.status = invoice.value.status;
      invoiceFormState.currency = invoice.value.currency || 'USD'; // New field
      invoiceFormState.notes = invoice.value.notes || '';

      // Initialize invoice items
      invoiceItems.value = invoice.value.items.map(item => ({
         id: item.id,
         description: item.description,
         quantity: item.quantity,
         unit_price: item.unit_price,
         total_price: item.total_price,
      }));
   }
   catch (err: any) {
      console.error('Error fetching invoice:', err);
      error.value = err.message || 'Failed to load invoice';
   }
   finally {
      isLoading.value = false;
   }
}

// Fetch clients for the dropdown
async function fetchClients() {
   try {
      const supabase = useSupabaseClient();

      if (!businessProfileId.value) {
         return;
      }

      clients.value = await getBusinessClients(supabase, businessProfileId.value, {
         activeOnly: true,
      });
   }
   catch (err: any) {
      console.error('Error fetching clients:', err);
   }
}

// Add a new invoice item
function addInvoiceItem() {
   invoiceItems.value.push({
      description: '',
      quantity: 1,
      unit_price: 0,
      total_price: 0,
      isNew: true,
   });
}

// Remove an invoice item
function removeInvoiceItem(index: number) {
   const item = invoiceItems.value[index];

   if (item.id) {
      // Mark existing items as deleted instead of removing them from the array
      item.isDeleted = true;
   }
   else {
      // Remove new items from the array
      invoiceItems.value.splice(index, 1);
   }
}

// Update item total price when quantity or unit price changes
function updateItemTotal(item: InvoiceFormItem) {
   item.total_price = item.quantity * item.unit_price;
}

// Toggle edit mode
function toggleEditMode() {
   isEditing.value = !isEditing.value;

   if (!isEditing.value && invoice.value) {
      // Reset form state when canceling edit
      invoiceFormState.client_id = invoice.value.client_id;
      invoiceFormState.issue_date = invoice.value.issue_date.split('T')[0]; // Changed from invoice_date
      invoiceFormState.due_date = invoice.value.due_date.split('T')[0];
      invoiceFormState.status = invoice.value.status;
      invoiceFormState.currency = invoice.value.currency || 'USD'; // New field
      invoiceFormState.notes = invoice.value.notes || '';

      // Reset invoice items
      invoiceItems.value = invoice.value.items.map(item => ({
         id: item.id,
         description: item.description,
         quantity: item.quantity,
         unit_price: item.unit_price,
         total_price: item.total_price,
      }));
   }
}

// Save invoice changes
async function saveInvoice() {
   isSubmitting.value = true;
   error.value = null;

   try {
      const supabase = useSupabaseClient();

      // Validate business profile ID
      if (!businessProfileId.value) {
         throw new Error('Business profile not found');
      }

      // Validate that we have at least one item
      if (invoiceItems.value.filter(item => !item.isDeleted).length === 0) {
         throw new Error('At least one invoice item is required');
      }

      // Validate that all items have a description and positive values
      for (const item of invoiceItems.value) {
         if (item.isDeleted) continue;

         if (!item.description.trim()) {
            throw new Error('All invoice items must have a description');
         }
         if (item.quantity <= 0) {
            throw new Error('All invoice items must have a quantity greater than zero');
         }
         if (item.unit_price < 0) {
            throw new Error('All invoice items must have a unit price of zero or greater');
         }
      }

      // Update invoice with items
      const updatedInvoice = await updateInvoiceWithItems(
         supabase,
         invoiceId,
         {
            client_id: invoiceFormState.client_id,
            issue_date: invoiceFormState.issue_date, // Changed from invoice_date
            due_date: invoiceFormState.due_date,
            status: invoiceFormState.status,
            currency: invoiceFormState.currency, // New field
            notes: invoiceFormState.notes || null,
            total: totalAmount.value, // Changed from total_amount
         },
         invoiceItems.value,
      );

      // Update local state with updated invoice
      invoice.value = updatedInvoice;

      // Exit edit mode
      isEditing.value = false;

      toast.add({
         title: 'Invoice Updated',
         description: 'Invoice has been updated successfully',
         color: 'success',
      });
   }
   catch (err: any) {
      console.error('Error updating invoice:', err);
      error.value = err.message || 'Failed to update invoice';

      toast.add({
         title: 'Error',
         description: err.message || 'There was a problem updating the invoice',
         color: 'danger',
      });
   }
   finally {
      isSubmitting.value = false;
   }
}

// Confirm delete invoice
function confirmDeleteInvoice() {
   // In a real implementation, you would show a confirmation dialog
   if (confirm('Are you sure you want to delete this invoice? This action cannot be undone.')) {
      deleteInvoiceAndRedirect();
   }
}

// Delete invoice and redirect to list
async function deleteInvoiceAndRedirect() {
   isSubmitting.value = true;
   error.value = null;

   try {
      const supabase = useSupabaseClient();

      await deleteInvoice(supabase, invoiceId);

      toast.add({
         title: 'Invoice Deleted',
         description: 'Invoice has been deleted successfully',
         color: 'success',
      });

      // Navigate to invoices list
      navigateTo('/invoices');
   }
   catch (err: any) {
      console.error('Error deleting invoice:', err);
      error.value = err.message || 'Failed to delete invoice';

      toast.add({
         title: 'Error',
         description: err.message || 'There was a problem deleting the invoice',
         color: 'danger',
      });
   }
   finally {
      isSubmitting.value = false;
   }
}

// Initial data fetch
onMounted(() => {
   fetchInvoice();
   fetchClients();
});
</script>

<template>
   <DefaultLayout>
      <div class="space-y-6">
         <!-- Header -->
         <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
               <UButton
                  variant="ghost"
                  color="neutral"
                  to="/invoices"
                  size="sm"
               >
                  <template #leading>
                     <UIcon name="i-heroicons-arrow-left" />
                  </template>
                  Back to Invoices
               </UButton>

               <h1 class="text-2xl font-bold">
                  Invoice Details
               </h1>
            </div>

            <div class="flex gap-2">
               <UButton
                  v-if="!isEditing"
                  color="primary"
                  @click="toggleEditMode"
               >
                  <template #leading>
                     <UIcon name="i-heroicons-pencil-square" />
                  </template>
                  Edit Invoice
               </UButton>
               <UButton
                  v-if="!isEditing"
                  color="danger"
                  variant="soft"
                  @click="confirmDeleteInvoice"
               >
                  <template #leading>
                     <UIcon name="i-heroicons-trash" />
                  </template>
                  Delete
               </UButton>
            </div>
         </div>

         <!-- Loading state -->
         <div v-if="isLoading" class="flex justify-center py-8">
            <UIcon name="i-heroicons-arrow-path" class="animate-spin h-8 w-8 text-gray-500" />
         </div>

         <!-- Error state -->
         <UAlert
            v-else-if="error"
            color="danger"
            variant="soft"
            title="Error"
            :description="error"
            class="mb-4"
         />

         <!-- Invoice details -->
         <template v-else-if="invoice">
            <UCard class="max-w-4xl">
               <!-- Invoice header -->
               <div class="flex flex-col md:flex-row justify-between mb-6">
                  <div>
                     <h2 class="text-xl font-semibold mb-2">
                        Invoice #{{ invoice.invoice_number || invoice.id.substring(0, 8) }}
                     </h2>
                     <UBadge
                        :color="statusColorMap[invoice.status]"
                        variant="subtle"
                        class="capitalize"
                     >
                        {{ invoice.status }}
                     </UBadge>
                  </div>
                  <div class="text-right mt-4 md:mt-0">
                     <div class="text-sm text-gray-500 mb-1">
                        Issue Date: {{ new Date(invoice.issue_date).toLocaleDateString() }}
                     </div>
                     <div class="text-sm text-gray-500">
                        Due Date: {{ new Date(invoice.due_date).toLocaleDateString() }}
                     </div>
                     <div v-if="invoice.currency" class="text-sm text-gray-500 mt-1">
                        Currency: {{ invoice.currency }}
                     </div>
                  </div>
               </div>

               <!-- Client and Invoice Details -->
               <div v-if="!isEditing" class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div>
                     <h3 class="text-sm font-medium text-gray-500 mb-2">
                        Client Information
                     </h3>
                     <div class="text-base font-medium">
                        {{ invoice.client_name }}
                     </div>
                     <div class="text-sm text-gray-600">
                        {{ invoice.client_email }}
                     </div>
                  </div>
                  <div>
                     <h3 class="text-sm font-medium text-gray-500 mb-2">
                        Amount Due
                     </h3>
                     <div class="text-2xl font-bold text-primary-600">
                        {{ new Intl.NumberFormat('en-US', { style: 'currency', currency: invoice.currency || 'USD' }).format(invoice.total || 0) }}
                     </div>
                  </div>
               </div>

               <!-- Edit Form -->
               <div v-if="isEditing" class="mb-8">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                     <UFormField label="Client" name="client_id" required>
                        <USelect
                           v-model="invoiceFormState.client_id"
                           :options="clients.map(client => ({ value: client.id, label: client.name }))"
                           placeholder="Select a client"
                           class="w-full"
                        />
                     </UFormField>

                     <UFormField label="Status" name="status" required>
                        <USelect
                           v-model="invoiceFormState.status"
                           :options="statusOptions"
                           placeholder="Select status"
                           class="w-full"
                        />
                     </UFormField>

                     <UFormField label="Issue Date" name="issue_date" required>
                        <UInput
                           v-model="invoiceFormState.issue_date"
                           type="date"
                           class="w-full"
                        />
                     </UFormField>

                     <UFormField label="Due Date" name="due_date" required>
                        <UInput
                           v-model="invoiceFormState.due_date"
                           type="date"
                           class="w-full"
                        />
                     </UFormField>

                     <UFormField label="Currency" name="currency">
                        <UInput
                           v-model="invoiceFormState.currency"
                           placeholder="USD"
                           class="w-full"
                        />
                     </UFormField>
                  </div>

                  <UFormField label="Notes" name="notes">
                     <UTextarea
                        v-model="invoiceFormState.notes"
                        placeholder="Additional notes or payment instructions"
                        :rows="3"
                        class="w-full"
                     />
                  </UFormField>
               </div>

               <!-- Invoice Items -->
               <div>
                  <div class="flex justify-between items-center mb-4">
                     <h3 class="text-lg font-medium">
                        Invoice Items
                     </h3>
                     <UButton
                        v-if="isEditing"
                        color="primary"
                        variant="soft"
                        size="sm"
                        @click="addInvoiceItem"
                     >
                        <template #leading>
                           <UIcon name="i-heroicons-plus" />
                        </template>
                        Add Item
                     </UButton>
                  </div>

                  <!-- Items Table -->
                  <div class="overflow-x-auto">
                     <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                           <tr>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                 Description
                              </th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                 Quantity
                              </th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                 Unit Price
                              </th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                 Total
                              </th>
                              <th v-if="isEditing" scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                 Actions
                              </th>
                           </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                           <tr
                              v-for="(item, index) in invoiceItems"
                              :key="item.id || index"
                              v-show="!item.isDeleted"
                              class="hover:bg-gray-50"
                           >
                              <td class="px-6 py-4 whitespace-nowrap">
                                 <UInput
                                    v-if="isEditing"
                                    v-model="item.description"
                                    placeholder="Item description"
                                    class="w-full"
                                 />
                                 <div v-else class="text-sm font-medium text-gray-900">
                                    {{ item.description }}
                                 </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                 <UInput
                                    v-if="isEditing"
                                    v-model.number="item.quantity"
                                    type="number"
                                    min="1"
                                    step="1"
                                    class="w-24"
                                    @update:model-value="updateItemTotal(item)"
                                 />
                                 <div v-else class="text-sm text-gray-900">
                                    {{ item.quantity }}
                                 </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                 <UInput
                                    v-if="isEditing"
                                    v-model.number="item.unit_price"
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    class="w-32"
                                    @update:model-value="updateItemTotal(item)"
                                 />
                                 <div v-else class="text-sm text-gray-900">
                                    {{ new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(item.unit_price) }}
                                 </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                 <div class="text-sm font-medium text-gray-900">
                                    {{ new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(item.total_price) }}
                                 </div>
                              </td>
                              <td v-if="isEditing" class="px-6 py-4 whitespace-nowrap text-right">
                                 <UButton
                                    color="danger"
                                    variant="ghost"
                                    size="sm"
                                    icon="i-heroicons-trash"
                                    @click="removeInvoiceItem(index)"
                                 />
                              </td>
                           </tr>
                        </tbody>
                        <tfoot>
                           <tr>
                              <td colspan="3" class="px-6 py-4 text-right font-medium">
                                 Total Amount:
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap font-bold text-lg">
                                 {{ new Intl.NumberFormat('en-US', { style: 'currency', currency: invoice.currency || 'USD' }).format(isEditing ? totalAmount : (invoice.total || 0)) }}
                              </td>
                              <td v-if="isEditing"></td>
                           </tr>
                        </tfoot>
                     </table>
                  </div>
               </div>

               <!-- Notes -->
               <div v-if="!isEditing && invoice.notes" class="mt-6">
                  <h3 class="text-sm font-medium text-gray-500 mb-2">
                     Notes
                  </h3>
                  <div class="text-sm text-gray-700 whitespace-pre-line">
                     {{ invoice.notes }}
                  </div>
               </div>

               <!-- Edit mode actions -->
               <div v-if="isEditing" class="flex justify-end gap-3 mt-6">
                  <UButton
                     type="button"
                     variant="outline"
                     color="neutral"
                     @click="toggleEditMode"
                  >
                     Cancel
                  </UButton>

                  <UButton
                     type="button"
                     color="primary"
                     :loading="isSubmitting"
                     @click="saveInvoice"
                  >
                     Save Changes
                  </UButton>
               </div>
            </UCard>
         </template>
      </div>
   </DefaultLayout>
</template>
