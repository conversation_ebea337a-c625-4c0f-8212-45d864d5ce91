<script setup lang="ts">
import type { TableColumn } from '@nuxt/ui';
import type { ColumnDef } from '@tanstack/table-core';
import type { BusinessClient } from '~/types/business-client';
import type { Invoice, InvoiceCurrency, InvoiceStatus } from '~/types/invoice';
import { IconsLibrary } from 'assets/icons';

import { storeToRefs } from 'pinia';
import { computed, h, onMounted, ref, resolveComponent, watch } from 'vue';
import SelectClient from '~/components/clients/select-client.vue';
import DatePicker from '~/components/form/date-picker.vue';
import InvoiceStatusBadge from '~/components/invoices/invoice-status-badge.vue';
import SelectMenuInvoiceStatus from '~/components/invoices/select-menu-invoice-status.vue';
import { getBusinessInvoices } from '~/services/database/invoice-service';
import { useBusinessProfileStore } from '~/store/business-profile-store';

/* ---------------------------------------------------------------------------------------------- */

// Resolve components
const UButton = resolveComponent('UButton');
const UIcon = resolveComponent('UIcon');
const UBadge = resolveComponent('UBadge');
const UDropdownMenu = resolveComponent('UDropdownMenu');

/* ---------------------------------------------------------------------------------------------- */

const businessProfileStore = useBusinessProfileStore();
const { businessProfileId } = storeToRefs(businessProfileStore);

/* ---------------------------------------------------------------------------------------------- */

// State for invoices data
const invoices = ref<Invoice[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);

// Pagination state
const page = ref(1);
const itemsPerPage = ref(10);
const totalItems = ref(0);

// Search, filter and sort state
const searchQuery = ref('');
const selectedStatus = ref<string>('');
const selectedClient = ref<string>('');
const startDate = ref<string>('');
const endDate = ref<string>('');

// Sorting state
const sorting = ref([{ id: 'issue_date', desc: true }]);

// Computed properties for sorting
const sortBy = computed(() => sorting.value[0]?.id || 'issue_date');
const sortOrder = computed(() => sorting.value[0]?.desc ? 'desc' : 'asc' as 'desc' | 'asc');

/* ---------------------------------------------------------------------------------------------- */

// Define table columns
const columns: ColumnDef<Invoice>[] = [
   {
      accessorKey: 'client_name',
      header: () => h('div', { class: 'text-left' }, 'Client'),
      cell: ({ row }) => {
         const invoice = row.original;

         return h('div', { class: 'flex flex-col' }, [
            h('div', { class: 'font-bold' }, invoice.client_name),
            h('div', { class: '' }, invoice.client_email),
         ]);
      },
   },

   {
      accessorKey: 'issue_date',
      header: () => h('div', { class: 'text-left' }, 'Issue Date'),
      cell: ({ row }) => {
         return new Date(row.getValue('issue_date')).toLocaleString('en-US', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
         });
      },
   },
   {
      accessorKey: 'due_date',
      header: () => h('div', { class: 'text-left' }, 'Due Date'),
      cell: ({ row }) => {
         return new Date(row.getValue('due_date')).toLocaleString('en-US', {
            day: 'numeric',
            month: 'short',
            year: 'numeric',
         });
      },
   },
   {
      accessorKey: 'total',
      header: () => h('div', { class: 'text-right' }, 'Total'),
      cell: ({ row }) => {
         const amount = Number.parseFloat(row.getValue('total'));
         const currency = row.original.currency || 'USD';
         const formatted = toFormattedCurrency(amount, currency as InvoiceCurrency);
         return h('div', { class: 'text-right font-medium' }, formatted);
      },
   },
   {
      accessorKey: 'status',
      header: () => h('div', { class: 'text-left' }, 'Status'),
      cell: ({ row }) => {
         return h(
            InvoiceStatusBadge,
            { variant: 'soft', status: row.getValue('status') as InvoiceStatus },
         );
      },
   },
   {
      accessorKey: 'actions',
      header: () => h('div', { class: 'text-right' }, 'Actions'),
      cell: ({ row }) => {
         const invoice = row.original;

         // Define dropdown items
         const dropdownItems = [
            [
               {
                  label: 'View',
                  icon: IconsLibrary.actions.view,
                  to: `/invoices/${invoice.id}`,
               },
               {
                  label: 'Duplicate',
                  icon: IconsLibrary.actions.duplicate,
                  to: `/invoices/new?duplicate=${invoice.id}`,
               },
            ],
         ];

         if (invoice.status === 'draft') {
            dropdownItems.push(
               [
                  {
                     label: 'Edit',
                     icon: IconsLibrary.actions.edit,
                     to: `/invoices/${invoice.id}/edit`,
                  },
               ],
            );
         }

         return h('div', { class: 'flex gap-2 justify-end' }, [
            h(UDropdownMenu, {
               items: dropdownItems,
               ui: {
                  content: 'w-48',
               },
            }, {
               default: () => h(UButton, {
                  color: 'primary',
                  variant: 'soft',
                  size: 'sm',
                  icon: 'i-heroicons-ellipsis-horizontal',
                  class: 'font-medium',
               }),
            }),
         ]);
      },
   },
];

/* ---------------------------------------------------------------------------------------------- */

// Fetch invoices from Supabase with filtering
async function fetchInvoices() {
   isLoading.value = true;
   error.value = null;
   invoices.value = [];

   try {
      const supabase = useSupabaseClient();

      if (!businessProfileId.value) {
         error.value = 'Business profile not found';
         return;
      }

      const options = {
         searchQuery: searchQuery.value || undefined,
         sortBy: sortBy.value,
         sortOrder: sortOrder.value,
         status: selectedStatus.value === 'all' ? undefined : selectedStatus.value || undefined,
         clientId: selectedClient.value || undefined,
         startDate: startDate.value || undefined,
         endDate: endDate.value || undefined,
         limit: itemsPerPage.value,
         offset: (page.value - 1) * itemsPerPage.value,
      };

      const result = await getBusinessInvoices(supabase, businessProfileId.value, options);
      invoices.value = result || [];
   }
   catch (err: any) {
      console.error('Error fetching invoices:', err);
      error.value = err.message || 'Failed to load invoices';
      invoices.value = []; // Ensure invoices is an empty array on error
   }
   finally {
      isLoading.value = false;
   }
}

/* ---------------------------------------------------------------------------------------------- */

/*
 * Reset filters
 */
function resetFilters() {
   searchQuery.value = '';
   selectedStatus.value = 'all';
   selectedClient.value = '';
   startDate.value = '';
   endDate.value = '';
}

/* ---------------------------------------------------------------------------------------------- */

watch([searchQuery, selectedStatus, selectedClient, startDate, endDate, page, sorting], () => {
   fetchInvoices();
}, { deep: true });

onMounted(() => {
   fetchInvoices();
});

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <DefaultLayout>
      <div class="flex flex-col gap-5 p-3">
         <!-- Header -->
         <div class="flex justify-between items-center">
            <h1 class="text-2xl font-bold">
               Invoices
            </h1>
            <UButton
               to="/invoices/new"
               color="primary"
               size="lg"
            >
               <template #leading>
                  <UIcon name="i-heroicons-document-plus" />
               </template>
               Create New Invoice
            </UButton>
         </div>

         <!-- Filters -->
         <UCard id="invoice-listing-filters">
            <div class="flex flex-col md:flex-row gap-4 flex-wrap">
               <!-- Search -->
               <UInput
                  v-model="searchQuery"
                  placeholder="Search invoices..."
                  size="lg"
                  class="w-full md:w-64"
               >
                  <template #leading>
                     <UIcon name="i-heroicons-magnifying-glass" />
                  </template>
               </UInput>

               <!-- Status filter -->
               <SelectMenuInvoiceStatus
                  v-model="selectedStatus"
                  enable-all
                  placeholder="Filter by status"
                  size="lg"
                  class="w-full md:w-48"
               />

               <!-- Client filter -->
               <div>
                  <SelectClient size="lg" name="client_id" select-only />
               </div>

               <!-- Date range filters -->
               <div class="flex gap-2 items-center">
                  <DatePicker v-model="startDate" size="lg" />
                  <span class="text-gray-500">to</span>
                  <DatePicker v-model="endDate" size="lg" />
               </div>

               <!-- Clear filters button -->
               <UButton
                  color="secondary"
                  variant="soft"
                  size="lg"
                  @click="resetFilters"
               >
                  <template #leading>
                     <UIcon :name="IconsLibrary.actions.clear" />
                  </template>
                  Clear Filters
               </UButton>
            </div>
         </UCard><!-- invoice filters -->

         <!-- Invoices Table -->
         <UCard>
            <!-- Loading state -->
            <div v-if="isLoading" class="flex justify-center py-8">
               <UIcon name="i-heroicons-arrow-path" class="animate-spin h-8 w-8 text-gray-500" />
            </div>

            <!-- Error state -->
            <UAlert
               v-else-if="error"
               color="error"
               variant="soft"
               title="Error"
               :description="error"
               class="mb-4"
            />

            <!-- Table -->
            <div v-else class="overflow-x-auto">
               <UTable
                  :columns="columns"
                  :data="invoices"
                  class="min-w-full"
               />

               <!-- Pagination -->
            </div>
         </UCard>
      </div>
   </DefaultLayout>
</template>
