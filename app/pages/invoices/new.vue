<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types';
import type { BusinessClient } from '~/types/business-client';
import type { InvoiceCurrency, InvoiceFormItem, InvoiceStatus } from '~/types/invoice';
import { useSupabaseClient } from '#imports';
import { IconsLibrary } from 'assets/icons';
import * as v from 'valibot';
import SelectClient from '~/components/clients/select-client.vue';
import DatePicker from '~/components/form/date-picker.vue';
import AddInvoiceItems from '~/components/invoices/add-invoice-items.vue';
import SelectMenuCurrency from '~/components/invoices/select-menu-currency.vue';
import SelectMenuInvoiceStatus from '~/components/invoices/select-menu-invoice-status.vue';
import { createInvoiceWithItems } from '~/services/database/invoice-service';
import { useBusinessProfileStore } from '~/store/business-profile-store';

/* ---------------------------------------------------------------------------------------------- */

const businessProfileStore = useBusinessProfileStore();
const { businessProfileId } = storeToRefs(businessProfileStore);
const route = useRoute();
const toast = useToast();

/* ---------------------------------------------------------------------------------------------- */

const preselectedClientId = route.query.client as string | undefined;

/* ---------------------------------------------------------------------------------------------- */

// Form validation schema
const invoiceSchema = v.object({
   client_id: v.pipe(v.string(), v.minLength(1, 'Client is required')),
   issue_date: v.pipe(v.string(), v.minLength(1, 'Issue date is required')), // Changed from invoice_date
   due_date: v.pipe(v.string(), v.minLength(1, 'Due date is required')),
   status: v.pipe(v.string()),
   currency: v.optional(v.string()),
   notes: v.optional(v.string()),
});

type InvoiceSchema = v.InferOutput<typeof invoiceSchema>;

// Form state
const invoiceFormState = reactive({
   client_id: preselectedClientId || '',
   issue_date: getTodayDateString(), // Today's date in YYYY-MM-DD format, changed from invoice_date
   due_date: '2025-05-30', // 30 days from now
   status: 'draft' as InvoiceStatus,
   currency: 'EUR' as InvoiceCurrency, // Default currency
   notes: '',
});

// Form submission state
const isSubmitting = ref(false);
const formError = ref('');

/* ---------------------------------------------------------------------------------------------- */
/*
 * Invoice items
 */

const invoiceItems = ref<InvoiceFormItem[]>([{
   description: '',
   quantity: 1,
   unit_price: 0,
   total_price: 0,
   isNew: true,
   tax: 0,
   discount: 0,
}]);
const invoiceTotal = ref(0);

/* ---------------------------------------------------------------------------------------------- */

// Handle form submission
async function handleSubmit(event: FormSubmitEvent<InvoiceSchema>) {
   isSubmitting.value = true;
   formError.value = '';

   try {
      const supabase = useSupabaseClient();

      // Validate business profile ID
      if (!businessProfileId.value) {
         formError.value = 'Business profile not found';
         return;
      }

      // Validate that we have at least one item
      if (invoiceItems.value.length === 0) {
         formError.value = 'At least one invoice item is required';
         return;
      }

      // Validate that all items have a description and positive values
      for (const item of invoiceItems.value) {
         if (!item.description.trim()) {
            formError.value = 'All invoice items must have a description';
            return;
         }
         if (item.quantity <= 0) {
            formError.value = 'All invoice items must have a quantity greater than zero';
            return;
         }
         if (item.unit_price < 0) {
            formError.value = 'All invoice items must have a unit price of zero or greater';
            return;
         }
      }

      // Create an invoice with items
      await createInvoiceWithItems(
         supabase,
         {
            business_id: businessProfileId.value,
            client_id: event.data.client_id,
            issue_date: event.data.issue_date,
            due_date: event.data.due_date,
            status: event.data.status as InvoiceStatus,
            currency: event.data.currency || 'USD',
            notes: event.data.notes || null,
            total: invoiceTotal.value,
         },
         invoiceItems.value.map(item => ({
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unit_price,
            total_price: item.total_price,
         })),
      );

      toast.add({
         title: 'Invoice Created',
         description: 'Invoice has been created successfully',
         color: 'success',
      });

      // Navigate to invoice list
      navigateTo('/invoices');
   }
   catch (error: any) {
      console.error('Error creating invoice:', error);
      formError.value = error.message || 'Failed to create invoice. Please try again.';

      toast.add({
         title: 'Error',
         description: error.message || 'There was a problem creating the invoice',
         color: 'error',
      });
   }
   finally {
      isSubmitting.value = false;
   }
}
</script>

<template>
   <DefaultLayout>
      <div class="space-y-6">
         <!-- Header -->
         <header class="flex items-center gap-4">
            <UButton
               variant="soft"
               color="neutral"
               to="/invoices"
               size="sm"
            >
               <template #leading>
                  <UIcon :name="IconsLibrary.actions.back" />
               </template>
               Back to Invoices
            </UButton>

            <h1 class="text-2xl font-bold">
               Create New Invoice
            </h1>
         </header>

         <!-- region: debug -->
         <div />
         <!-- endregion: debug -->

         <!-- Invoice form -->
         <UCard class="">
            <UForm
               :schema="invoiceSchema"
               :state="invoiceFormState"
               class="flex flex-col gap-6 w-full"
               @submit="handleSubmit"
            >
               <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <UFormField label="Client">
                     <SelectClient v-model:client-id="invoiceFormState.client_id" name="client_id" />
                  </UFormField>
               </div>

               <!-- Client and Dates Section -->
               <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                  <UFormField label="Status" name="status" required>
                     <SelectMenuInvoiceStatus
                        v-model="invoiceFormState.status"
                        placeholder="Select status"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Issue Date" name="issue_date" required>
                     <DatePicker
                        v-model="invoiceFormState.issue_date"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Due Date" name="due_date" required>
                     <DatePicker
                        v-model="invoiceFormState.due_date"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Currency" name="currency">
                     <SelectMenuCurrency
                        v-model="invoiceFormState.currency"
                     />
                  </UFormField>
               </div>

               <!-- region: Invoice items -->
               <section>
                  <AddInvoiceItems v-model="invoiceItems" :currency="invoiceFormState.currency" />
               </section>
               <!-- endregion: Invoice items -->

               <!-- Notes Section -->
               <div class="mt-6">
                  <UFormField label="Notes" name="notes">
                     <UTextarea
                        v-model="invoiceFormState.notes"
                        placeholder="Additional notes or payment instructions"
                        :rows="3"
                        class="w-full"
                     />
                  </UFormField>
               </div>

               <!-- Error Alert -->
               <div v-if="formError" class="mt-4">
                  <UAlert
                     color="error"
                     variant="soft"
                     title="Error"
                     :description="formError"
                  />
               </div>

               <!-- Form Actions -->
               <div class="flex justify-end gap-3 pt-4">
                  <UButton
                     type="button"
                     variant="outline"
                     color="neutral"
                     to="/invoices"
                  >
                     Cancel
                  </UButton>

                  <UButton
                     type="submit"
                     color="primary"
                     :loading="isSubmitting"
                  >
                     Create Invoice
                  </UButton>
               </div>
            </UForm> <!-- form -->
         </UCard>

         <div>
            <pre>{{ invoiceFormState }}</pre>

            <pre>{{ invoiceItems }}</pre>
         </div>
      </div>
   </DefaultLayout>
</template>
