<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types';
import * as v from 'valibot';
import { getBusinessProfile } from '~/services/database/business-service';

/* ---------------------------------------------------------------------------------------------- */

const client = useSupabaseClient();

/* ---------------------------------------------------------------------------------------------- */

const isLoading = ref(false);
const formErrors = ref('');

const signInSchema = v.object({
   email: v.pipe(v.string(), v.email('Invalid email')),
   password: v.pipe(v.string(), v.minLength(6, 'Password must be at least 6 characters')),

});

type SignInSchema = v.InferOutput<typeof signInSchema>;

const signInFormState = ref({
   email: '<EMAIL>',
   password: 'saravana@123',
});

/* ------------------------------------------------------------------------------------------- */

async function handleSubmit(event: FormSubmitEvent<SignInSchema>) {
   isLoading.value = true;

   const { email, password } = event.data;

   const { error } = await client.auth.signInWithPassword({
      email,
      password,
   });

   if (error) {
      isLoading.value = false;
      formErrors.value = error.message;
      return;
   }

   // check if business profile exists
   const user = useSupabaseUser();
   if (user.value) {
      try {
         const result = await getBusinessProfile(client, user.value.id);

         console.log(result);

         if (!result) {
            navigateTo('/user/setup');
            return;
         }
      }
      catch (error) {
         console.error(error);
      }
   }

   isLoading.value = false;

   navigateTo('/');
}

/* ---------------------------------------------------------------------------------------------- */
</script>

<template>
   <AuthLayout>
      <template #description>
         <p>Welcome back! Sign in to your account</p>
      </template>

      <template #subtitle>
         <p>Sign in</p>
      </template>

      <main>
         <div class="flex flex-col gap-4">
            <UForm
               :state="signInFormState"
               :schema="signInSchema"
               class="flex flex-col gap-4"
               @submit="handleSubmit"
            >
               <UFormField label="Email" required size="lg" name="email">
                  <UInput
                     v-model="signInFormState.email"
                     placeholder="Enter your email" class=" w-full" type="email"
                  />
               </UFormField>

               <div class="flex flex-col gap-2">
                  <UFormField label="Password" required size="lg" name="password">
                     <UInput
                        v-model="signInFormState.password"
                        placeholder="Enter your password" type="password" class="w-full"
                     />
                  </UFormField>

                  <div class="flex justify-end">
                     <NuxtLink class="text-sm text-primary-500 hover:text-primary-600 font-medium">
                        Forgot password?
                     </NuxtLink>
                  </div>
               </div>

               <UButton type="submit" block label="Sign in" size="lg" :loading="isLoading" />
            </UForm>

            <USeparator label="or continue with Google" />

            <UButton
               block label="Sign in with Google" color="secondary"
               variant="outline" icon="logos:google-icon" size="lg"
            />

            <template v-if="formErrors">
               <UAlert color="error" variant="subtle" title="Error!" :description="formErrors" />
            </template>
         </div>
      </main>

      <template #footer>
         <div class="text-sm">
            Don't have an account yet?
            <NuxtLink
               class="text-sm text-primary-500 hover:text-primary-600 font-medium"
               to="/sign-up"
            >
               Sign up
            </NuxtLink>
         </div>
      </template>
   </AuthLayout>
</template>

<style scoped>

</style>
