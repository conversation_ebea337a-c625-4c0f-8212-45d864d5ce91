<script setup lang="ts">
import SelectClient from '~/components/clients/select-client.vue';

const selectedClientId = ref('');
const selectedClientName = ref('');

function handleCreateClient() {
  alert('Create client functionality will be implemented later');
}
</script>

<template>
  <div class="container mx-auto p-8">
    <h1 class="text-2xl font-bold mb-6">Test Client Select Component</h1>
    
    <div class="max-w-md">
      <SelectClient 
        v-model:client="selectedClientName"
        v-model:ClientId="selectedClientId"
        name="client"
        @create-client="handleCreateClient"
      />
      
      <div class="mt-8 p-4 bg-gray-100 rounded-md">
        <h2 class="font-semibold mb-2">Selected Values:</h2>
        <p><strong>Client ID:</strong> {{ selectedClientId || 'None' }}</p>
        <p><strong>Client Name:</strong> {{ selectedClientName || 'None' }}</p>
      </div>
    </div>
  </div>
</template>
