<script setup lang="ts">
import type { TableColumn } from '@nuxt/ui';
import type { BusinessClient } from '~/types/business-client';
import { storeToRefs } from 'pinia';
import { h, onMounted, ref, resolveComponent, watch } from 'vue';

import { getBusinessClients } from '~/services/database/business-client-service';
import { useBusinessProfileStore } from '~/store/business-profile-store';

const UButton = resolveComponent('UButton');
const UIcon = resolveComponent('UIcon');
const UBadge = resolveComponent('UBadge');
const UDropdownMenu = resolveComponent('UDropdownMenu');

/* ---------------------------------------------------------------------------------------------- */

// State for clients data
const clients = ref<BusinessClient[]>([]);
const isLoading = ref(true);
const error = ref<string | null>(null);

/* ---------------------------------------------------------------------------------------------- */

// Get business profile store and extract businessProfileId
const businessProfileStore = useBusinessProfileStore();
const { businessProfileId } = storeToRefs(businessProfileStore);

// Search, filter and sort state
const searchQuery = ref('');
const sortBy = ref('name');
const sortOrder = ref<'asc' | 'desc'>('asc');
// When true, filter to show only active clients; when false, show all clients (active and inactive)
const showActiveOnly = ref(true);

// No responsive breakpoints needed for simple scrollable table

// Table columns definition
const columns: TableColumn<BusinessClient>[] = [
   {
      accessorKey: 'name',
      header: 'Client Name',
   },
   {
      accessorKey: 'email',
      header: 'Email',
   },
   {
      accessorKey: 'active',
      header: 'Status',
      cell: ({ row }) => {
         const isActive = row.getValue('active');
         return h(UBadge, {
            color: isActive ? 'success' : 'gray',
            variant: 'subtle',
            class: 'capitalize',
         }, () => isActive ? 'Active' : 'Inactive');
      },
   },
   {
      accessorKey: 'phone',
      header: 'Phone',
      cell: ({ row }) => row.getValue('phone') || '—',
   },
   {
      accessorKey: 'company_name',
      header: 'Company',
      cell: ({ row }) => row.getValue('company_name') || '—',
   },
   {
      id: 'actions',
      header: () => h('div', { class: 'text-right' }, 'Actions'),
      cell: ({ row }) => {
         const client = row.original;

         // Define dropdown items
         const dropdownItems = [
            [
               {
                  label: 'Edit',
                  icon: 'i-heroicons-pencil-square',
                  to: `/clients/${client.id}/edit`,
               },
               {
                  label: 'Add New Invoice',
                  icon: 'i-heroicons-document-plus',
                  to: `/invoices/new?client=${client.id}`,
               }
            ]
         ];

         return h('div', { class: 'flex gap-2 justify-end' }, [
            h(UDropdownMenu, {
               items: dropdownItems,
               ui: {
                  content: 'w-48'
               }
            }, {
               default: () => h(UButton, {
                  color: 'primary',
                  variant: 'soft',
                  size: 'sm',
                  icon: 'i-heroicons-ellipsis-horizontal',
                  class: 'font-medium',
               })
            }),
         ]);
      },
   },
];

// Watch for search query changes and refresh results
watchDebounced(searchQuery, async (newValue) => {
   if (newValue !== undefined) {
      await fetchClients();
   }
}, { debounce: 300 });

// Watch for active filter changes and refresh results
watch(showActiveOnly, async () => {
   await fetchClients();
});

// Ensure the business profile is loaded
onMounted(async () => {
   // If businessProfileId is not available, try to load it
   if (!businessProfileId.value) {
      const user = useSupabaseUser();
      if (user.value?.id) {
         await businessProfileStore.fetchProfile(user.value.id);
      }
   }

   await fetchClients();
});

// Fetch clients from Supabase with filtering
async function fetchClients() {
   isLoading.value = true;
   error.value = null;

   try {
      const supabase = useSupabaseClient();

      if (!businessProfileId.value) {
         error.value = 'Business profile not found';
         return;
      }

      const options = {
         searchQuery: searchQuery.value || undefined,
         sortBy: sortBy.value,
         sortOrder: sortOrder.value,
         // When true, filter to show only active clients; when false, show all clients (active and inactive)
         activeOnly: showActiveOnly.value,
      };

      clients.value = await getBusinessClients(supabase, businessProfileId.value, options);
   }
   catch (err: any) {
      console.error('Error fetching clients:', err);
      error.value = err.message || 'Failed to load clients';
   }
   finally {
      isLoading.value = false;
   }
}

// Handle sort
function handleSort(column: string) {
   if (sortBy.value === column) {
      // Toggle sort order if clicking the same column
      sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
   }
   else {
      // Set a new sort column and default to ascending
      sortBy.value = column;
      sortOrder.value = 'asc';
   }

   fetchClients();
}

// Sort handler for the table
const sort = {
   column: sortBy,
   direction: sortOrder,
   onSort: (column: string) => handleSort(column),
};
</script>

<template>
   <DefaultLayout>
      <div class="flex flex-col gap-5 p-3">
         <!-- Header -->
         <div class="flex justify-between items-center">
            <h1 class="text-2xl font-bold">
               Clients
            </h1>
            <UButton
               to="/clients/new"
               color="primary"
               size="lg"
            >
               <template #leading>
                  <UIcon name="i-heroicons-plus" />
               </template>
               Add New Client
            </UButton>
         </div>

         <!-- Search and filters -->
         <div class="flex flex-col md:flex-row gap-4">
            <UInput
               v-model="searchQuery"
               placeholder="Search clients..."
               size="lg"
               class="w-full md:w-80"
            >
               <template #leading>
                  <UIcon name="i-heroicons-magnifying-glass" />
               </template>
            </UInput>

            <div class="flex items-center gap-2">
               <USwitch
                  v-model="showActiveOnly"
                  checked-icon="i-heroicons-check"
                  unchecked-icon="i-heroicons-x-mark"
                  color="primary"
               />
               <span class="font-medium" :class="{ 'text-primary-500': showActiveOnly }">
                  {{ showActiveOnly ? 'Showing active clients only' : 'Showing all clients' }}
               </span>
            </div>
         </div>

         <!-- Loading state -->
         <div v-if="isLoading" class="flex justify-center py-8">
            <UIcon name="i-heroicons-arrow-path" class="animate-spin h-8 w-8 text-gray-500" />
         </div>

         <!-- Error state -->
         <UAlert v-else-if="error" color="error" variant="soft" title="Error" :description="error" />

         <!-- Empty state -->
         <div v-else-if="clients.length === 0" class="flex flex-col items-center justify-center py-12">
            <UIcon name="i-heroicons-users" class="text-gray-400 mb-4" size="xl" />
            <p class="text-gray-500 mb-4">
               No clients found
            </p>
            <UButton
               to="/clients/new"
               color="primary"
               variant="soft"
            >
               Add your first client
            </UButton>
         </div>

         <!-- Clients table -->
         <UCard class="">
            <div v-if="!showActiveOnly" class="mb-4 px-4 py-2 bg-gray-50 dark:bg-gray-800/50 rounded-md flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
               <UIcon name="i-heroicons-information-circle" class="flex-shrink-0" />
               <span>Both active and inactive clients are displayed. Inactive clients are marked with an "Inactive" status.</span>
            </div>

            <div class="overflow-x-auto">
               <UTable
                  :data="clients"
                  :columns="columns"
                  :sort="sort"
                  class="min-w-full"
               >
                  <template #empty-state>
                     <div class="flex flex-col items-center justify-center py-6">
                        <UIcon name="i-heroicons-users" class="text-gray-400 mb-2" size="xl" />
                        <p class="text-gray-500 mb-2">
                           No clients found
                        </p>
                        <UButton
                           to="/clients/new"
                           color="primary"
                           variant="soft"
                           size="sm"
                        >
                           Add your first client
                        </UButton>
                     </div>
                  </template>
               </UTable>
            </div>
         </UCard>
      </div>
   </DefaultLayout>
</template>
