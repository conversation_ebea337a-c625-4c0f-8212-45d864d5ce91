<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types';
import { useSupabaseClient } from '#imports';
import * as v from 'valibot';
import { createBusinessClient } from '~/services/database/business-client-service';
import { useBusinessProfileStore } from '~/store/business-profile-store';

/* ---------------------------------------------------------------------------------------------- */

const businessProfileStore = useBusinessProfileStore();
const { businessProfileId } = storeToRefs(businessProfileStore);

/* ---------------------------------------------------------------------------------------------- */

// Form validation schema
const clientSchema = v.object({
   name: v.pipe(v.string(), v.minLength(1, 'Client name is required')),
   email: v.pipe(v.string(), v.email('Invalid email address')),
   phone: v.optional(v.string()),
   address: v.optional(v.string()),
   company_name: v.optional(v.string()),
   client_number: v.optional(v.number()),
});

type ClientSchema = v.InferOutput<typeof clientSchema>;

// Form state
const clientFormState = reactive({
   name: '',
   email: '',
   phone: '',
   address: '',
   company_name: '',
   client_number: undefined as number | undefined,
});

const isSubmitting = ref(false);
const formError = ref('');

// Handle form submission
async function handleSubmit(event: FormSubmitEvent<ClientSchema>) {
   isSubmitting.value = true;
   formError.value = '';

   try {
      const supabase = useSupabaseClient();

      if (!businessProfileId.value) {
         formError.value = 'Business profile not found';
         return;
      }

      await createBusinessClient(supabase, businessProfileId.value, event.data);
      navigateTo('/clients');
   }
   catch (error: any) {
      console.error('Error saving client:', error);
      formError.value = error.message || 'Failed to save client. Please try again.';
   }
   finally {
      isSubmitting.value = false;
   }
}
</script>

<template>
   <DefaultLayout>
      <div class="space-y-6">
         <!-- Header -->
         <div class="flex items-center gap-4">
            <UButton
               variant="ghost"
               color="neutral"
               to="/clients"
               size="sm"
            >
               <template #leading>
                  <UIcon name="i-heroicons-arrow-left" />
               </template>
               Back to Clients
            </UButton>

            <h1 class="text-2xl font-bold">
               Add New Client
            </h1>
         </div>

         <!-- region: debug -->
         <div>
            <pre>{{ businessProfileId }}</pre>
         </div>
         <!-- endregion: debug -->

         <!-- Client form -->
         <UCard class="max-w-3xl">
            <!-- ...other code remains unchanged... -->

            <UForm
               :schema="clientSchema"
               :state="clientFormState"
               class="flex flex-col gap-4 w-full"
               @submit="handleSubmit"
            >
               <h3 class="text-lg font-medium mb-2">
                  Client Information
               </h3>
               <p class="text-sm text-gray-500 mb-4">
                  Enter the basic information about your client
               </p>

               <div class="space-y-4">
                  <UFormField label="Client Name" name="name" required class="w-full">
                     <UInput
                        v-model="clientFormState.name"
                        placeholder="Enter client name"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Company Name" name="company_name" class="w-full">
                     <UInput
                        v-model="clientFormState.company_name"
                        placeholder="Enter company name"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Client Number" name="client_number" class="w-full">
                     <UInput
                        v-model="clientFormState.client_number"
                        type="number"
                        placeholder="Client reference number"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Email Address" name="email" required class="w-full">
                     <UInput
                        v-model="clientFormState.email"
                        type="email"
                        placeholder="<EMAIL>"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Phone Number" name="phone" class="w-full">
                     <UInput
                        v-model="clientFormState.phone"
                        placeholder="(*************"
                        class="w-full"
                     />
                  </UFormField>
               </div>

               <div class="mt-6">
                  <h3 class="text-lg font-medium mb-2">
                     Address
                  </h3>
                  <p class="text-sm text-gray-500 mb-4">
                     Client's billing address
                  </p>

                  <UFormField label="Address" name="address" class="w-full">
                     <UTextarea
                        v-model="clientFormState.address"
                        placeholder="Enter client's address"
                        :rows="3"
                        class="w-full"
                     />
                  </UFormField>
               </div>

               <div v-if="formError" class="mt-4">
                  <UAlert
                     color="error"
                     variant="soft"
                     title="Error"
                     :description="formError"
                  />
               </div>

               <div class="flex justify-end gap-3 pt-4">
                  <UButton
                     type="button"
                     variant="outline"
                     color="neutral"
                     to="/clients"
                  >
                     Cancel
                  </UButton>

                  <UButton
                     type="submit"
                     color="primary"
                     :loading="isSubmitting"
                  >
                     Save Client
                  </UButton>
               </div>
            </UForm>
         </UCard>
      </div>
   </DefaultLayout>
</template>
