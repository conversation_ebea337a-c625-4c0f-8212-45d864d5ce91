<script setup lang="ts">
import type { FormSubmitEvent } from '#ui/types';
import { useSupabaseClient } from '#imports';
import { IconsLibrary } from 'assets/icons';
import * as v from 'valibot';
import {
   getBusinessClientById,
   softDeleteClient,
   updateBusinessClient,
} from '~/services/database/business-client-service';
import { useBusinessProfileStore } from '~/store/business-profile-store';

/* ---------------------------------------------------------------------------------------------- */

const route = useRoute();
const clientId = route.params.id as string;
const businessProfileStore = useBusinessProfileStore();
const { businessProfileId } = storeToRefs(businessProfileStore);
const toast = useToast();

/* ---------------------------------------------------------------------------------------------- */

// Form validation schema
const clientSchema = v.object({
   name: v.pipe(v.string(), v.minLength(1, 'Client name is required')),
   email: v.pipe(v.string(), v.email('Invalid email address')),
   phone: v.optional(v.string()),
   address: v.optional(v.string()),
   company_name: v.optional(v.string()),
   client_number: v.optional(v.number()),
   active: v.boolean(),
});

type ClientSchema = v.InferOutput<typeof clientSchema>;

// Form state
const clientFormState = reactive({
   name: '',
   email: '',
   phone: '',
   address: '',
   company_name: '',
   client_number: null as number | null,
   active: true,
});

const isLoading = ref(true);
const isSubmitting = ref(false);
const isDeleting = ref(false);
const formError = ref('');

// Fetch client data
async function fetchClient() {
   isLoading.value = true;
   formError.value = '';

   try {
      const supabase = useSupabaseClient();

      // Validate client ID
      if (!clientId) {
         throw new Error('Client ID is missing');
      }

      const client = await getBusinessClientById(supabase, clientId);

      // Update form state with client data
      clientFormState.name = client.name;
      clientFormState.email = client.email;
      clientFormState.phone = client.phone || '';
      clientFormState.address = client.address || '';
      clientFormState.company_name = client.company_name || '';
      clientFormState.client_number = client.client_number;
      clientFormState.active = client.active;
   }
   catch (error: any) {
      console.error('Error fetching client:', error);
      formError.value = error.message || 'Failed to load client. Please try again.';

      toast.add({
         title: 'Error',
         description: error.message || 'Failed to load client details',
         color: 'error',
      });
   }
   finally {
      isLoading.value = false;
   }
}

// Handle form submission
async function handleSubmit(event: FormSubmitEvent<ClientSchema>) {
   isSubmitting.value = true;
   formError.value = '';

   try {
      const supabase = useSupabaseClient();

      // Validate client ID
      if (!clientId) {
         throw new Error('Client ID is missing');
      }

      await updateBusinessClient(supabase, clientId, event.data);

      toast.add({
         title: 'Client Updated',
         description: 'Client information has been updated successfully',
         color: 'success',
      });

      navigateTo('/clients');
   }
   catch (error: any) {
      console.error('Error updating client:', error);
      formError.value = error.message || 'Failed to update client. Please try again.';

      toast.add({
         title: 'Update Failed',
         description: error.message || 'There was a problem updating the client',
         color: 'error',
      });
   }
   finally {
      isSubmitting.value = false;
   }
}

// Handle client deletion
async function handleDeleteClient() {
   if (!confirm('Are you sure you want to delete this client? This action cannot be undone.')) {
      return;
   }

   isDeleting.value = true;
   formError.value = '';

   try {
      const supabase = useSupabaseClient();

      // Validate client ID
      if (!clientId) {
         throw new Error('Client ID is missing');
      }

      await softDeleteClient(supabase, clientId);

      toast.add({
         title: 'Client Deleted',
         description: 'Client has been successfully deleted',
         color: 'success',
      });

      navigateTo('/clients');
   }
   catch (error: any) {
      console.error('Error deleting client:', error);
      formError.value = error.message || 'Failed to delete client. Please try again.';

      toast.add({
         title: 'Delete Failed',
         description: error.message || 'There was a problem deleting the client',
         color: 'error',
      });
   }
   finally {
      isDeleting.value = false;
   }
}

// Load client data on mount
onMounted(async () => {
   // If businessProfileId is not available, try to load it
   if (!businessProfileId.value) {
      const user = useSupabaseUser();
      if (user.value?.id) {
         await businessProfileStore.fetchProfile(user.value.id);
      }
   }

   await fetchClient();
});
</script>

<template>
   <DefaultLayout>
      <div class="space-y-6">
         <!-- Header -->
         <div class="flex items-center gap-4">
            <UButton
               variant="ghost"
               color="neutral"
               to="/clients"
               size="sm"
            >
               <template #leading>
                  <UIcon name="i-heroicons-arrow-left" />
               </template>
               Back to Clients
            </UButton>

            <h1 class="text-2xl font-bold">
               Edit Client
            </h1>
         </div>

         <!-- Loading state -->
         <div v-if="isLoading" class="flex justify-center py-8">
            <UIcon name="i-heroicons-arrow-path" class="animate-spin h-8 w-8 text-gray-500" />
         </div>

         <!-- Error state -->
         <UAlert
            v-else-if="formError && !isSubmitting" color="error" variant="soft" title="Error"
            :description="formError"
         />

         <!-- Client form -->
         <UCard v-else class="max-w-3xl">
            <UForm
               :schema="clientSchema"
               :state="clientFormState"
               class="flex flex-col gap-4 w-full"
               @submit="handleSubmit"
            >
               <h3 class="text-lg font-medium mb-2">
                  Client Information
               </h3>
               <p class="text-sm text-gray-500 mb-4">
                  Update the information about your client
               </p>

               <div class="space-y-4">
                  <UFormField label="Client Name" name="name" required class="w-full">
                     <UInput
                        v-model="clientFormState.name"
                        placeholder="Enter client name"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Company Name" name="company_name" class="w-full">
                     <UInput
                        v-model="clientFormState.company_name"
                        placeholder="Enter company name"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Client Number" name="client_number" class="w-full">
                     <UInput
                        v-model.number="clientFormState.client_number"
                        type="number"
                        placeholder="Client reference number"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Email" name="email" required class="w-full">
                     <UInput
                        v-model="clientFormState.email"
                        type="email"
                        placeholder="Enter client email"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Phone" name="phone" class="w-full">
                     <UInput
                        v-model="clientFormState.phone"
                        placeholder="Enter client phone"
                        class="w-full"
                     />
                  </UFormField>

                  <UFormField label="Address" name="address" class="w-full">
                     <UTextarea
                        v-model="clientFormState.address"
                        placeholder="Enter client address"
                        class="w-full"
                        :rows="3"
                     />
                  </UFormField>

                  <UFormField label="Active Status" name="active" class="w-full">
                     <USwitch
                        v-model="clientFormState.active"
                        :checked-icon="IconsLibrary.actions.check"
                        :unchecked-icon="IconsLibrary.actions.uncheck"
                        color="primary"
                     >
                        {{ clientFormState.active ? 'Active' : 'Inactive' }}
                     </USwitch>
                  </UFormField>
               </div>

               <div v-if="formError" class="mt-4">
                  <UAlert
                     color="error"
                     variant="soft"
                     title="Error"
                     :description="formError"
                  />
               </div>

               <div class="flex justify-end gap-3 pt-4">
                  <UButton
                     type="button"
                     variant="outline"
                     color="neutral"
                     to="/clients"
                  >
                     Cancel
                  </UButton>

                  <UButton
                     type="submit"
                     color="primary"
                     :loading="isSubmitting"
                  >
                     Update Client
                  </UButton>
               </div>
            </UForm>

            <!-- Danger Zone -->
            <div class="mt-8 pt-6 border-t border-gray-200">
               <h3 class="text-lg font-medium text-error-600 mb-2">
                  Danger Zone
               </h3>
               <p class="text-sm text-gray-500 mb-4">
                  Actions in this section cannot be undone
               </p>

               <UButton
                  color="error"
                  variant="ghost"
                  :loading="isDeleting"
                  @click="handleDeleteClient"
               >
                  <template #leading>
                     <UIcon name="i-heroicons-trash" />
                  </template>
                  Delete Client
               </UButton>
            </div>
         </UCard>
      </div>
   </DefaultLayout>
</template>
