<script setup lang="ts">
import { useBusinessProfileStore } from '~/store/business-profile-store';

const config = useRuntimeConfig();

const businessProfileStore = useBusinessProfileStore();
</script>

<template>
   <DefaultLayout>
      <div class="max-w-7xl mx-auto px-4 py-12 text-center">
         <h1 class="text-4xl md:text-5xl font-bold mb-2 text-slate-800">
            Welcome to <span class="text-blue-500">AirInvoice</span>
         </h1>
         <p class="text-xl text-slate-600 mb-12">
            Create professional invoices in minutes
         </p>

         <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg hover:-translate-y-1 transition-all">
               <Icon name="heroicons:document-text" class="text-5xl text-blue-500 mb-4" />
               <h3 class="text-xl font-semibold text-slate-800 mb-3">
                  Create Invoices
               </h3>
               <p class="text-slate-600 leading-relaxed">
                  Generate professional invoices with customizable templates
               </p>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg hover:-translate-y-1 transition-all">
               <Icon name="heroicons:currency-dollar" class="text-5xl text-blue-500 mb-4" />
               <h3 class="text-xl font-semibold text-slate-800 mb-3">
                  Track Payments
               </h3>
               <p class="text-slate-600 leading-relaxed">
                  Monitor payment statuses and send reminders
               </p>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg hover:-translate-y-1 transition-all">
               <Icon name="heroicons:chart-bar" class="text-5xl text-blue-500 mb-4" />
               <h3 class="text-xl font-semibold text-slate-800 mb-3">
                  Business Insights
               </h3>
               <p class="text-slate-600 leading-relaxed">
                  View reports and analytics of your financial data
               </p>
            </div>
         </div>

         <div class="flex flex-wrap gap-4 justify-center">
            <Button color="primary" size="lg">
               Create New Invoice
            </Button>
            <Button variant="outline" size="lg">
               View Dashboard
            </Button>
         </div>
      </div>
   </DefaultLayout>
</template>

<style scoped>

</style>
