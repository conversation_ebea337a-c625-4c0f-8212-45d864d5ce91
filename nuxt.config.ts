// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
   compatibilityDate: '2024-11-01',
   devtools: { enabled: true },

   future: {
      compatibilityVersion: 4,
   },

   css: ['~/assets/css/main.css'],

   ui: {
      colorMode: false,
   },

   components: [
      { path: '~/components' },
      { path: '~/page-layouts' },
   ],

   modules: [
      '@nuxt/eslint',
      '@nuxt/fonts',
      '@nuxt/icon',
      '@nuxt/image',
      '@nuxt/scripts',
      '@nuxt/ui',
      '@nuxtjs/supabase',
      '@vueuse/nuxt',
      '@pinia/nuxt',
      'dayjs-nuxt',
   ],

   eslint: {
      config: {
         standalone: false,
      },
   },

   supabase: {
      redirectOptions: {
         login: '/sign-in',
         callback: '/callback',
         exclude: ['/sign-up', '/welcome', '/sign-up-complete'],
      },
   },

   runtimeConfig: {
      public: {
         appBaseUrl: '',
         // eslint-disable-next-line node/prefer-global/process
         appVersion: process.env.npm_package_version || '0.1',
      },
   },

});
