{"name": "nuxt-app", "type": "module", "private": true, "version": "0.7", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "eslint": "eslint --fix"}, "dependencies": {"@nuxt/eslint": "1.3.0", "@nuxt/fonts": "0.11.2", "@nuxt/icon": "1.12.0", "@nuxt/image": "1.10.0", "@nuxt/scripts": "0.11.6", "@nuxt/ui": "3.1.2", "@nuxtjs/supabase": "^1.5.0", "@pinia/nuxt": "^0.11.0", "@unhead/vue": "^2.0.3", "@vueuse/nuxt": "^13.1.0", "dayjs-nuxt": "^2.1.11", "eslint": "^9.0.0", "nuxt": "^3.17.1", "pinia": "^3.0.2", "tailwindcss": "^4.1.4", "typescript": "^5.6.3", "valibot": "^1.0.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "eslint": "^9.24.0"}, "resolutions": {"cookie": "0.7.2"}}